using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using MudBlazor;
using TaskTracking.TaskGroupAggregate;
using TaskTracking.TaskGroupAggregate.Dtos.TaskItems;
using TaskTracking.TaskGroupAggregate.TaskItems;
using Volo.Abp.Application.Dtos;

namespace TaskTracking.Blazor.Client.Pages;

// Enums for filtering
public enum TaskStatusFilter
{
    All,
    Pending,
    Completed,
    Overdue
}

public enum TaskTypeFilter
{
    All,
    OneTime,
    Recurring
}

public partial class TodayTasks
{
    [Inject] private ITaskItemAppService TaskItemAppService { get; set; } = null!;
    [Inject] private NavigationManager NavigationManager { get; set; } = null!;
    [Inject] private ISnackbar Snackbar { get; set; } = null!;

    private List<TaskItemDto> Tasks { get; set; } = new();
    private List<TaskItemDto> FilteredTasks { get; set; } = new();
    private bool IsLoading { get; set; } = true;
    private bool IsLoadingMore { get; set; } = false;
    private bool HasMoreData { get; set; } = true;
    private int CurrentPage { get; set; } = 0;
    private int TotalTasksCount { get; set; } = 0;
    private const int PageSize = 20;

    // Filter properties
    private string SearchText { get; set; } = string.Empty;
    private TaskStatusFilter StatusFilter { get; set; } = TaskStatusFilter.All;
    private TaskTypeFilter TaskTypeFilter { get; set; } = TaskTypeFilter.All;

    // Statistics
    private int CompletedTasksCount => Tasks.Count(t => t.IsCompleted);
    private int PendingTasksCount => Tasks.Count(t => !t.IsCompleted);

    protected override async Task OnInitializedAsync()
    {
        await LoadTasks();
    }

    private async Task LoadTasks()
    {
        try
        {
            IsLoading = true;
            CurrentPage = 0;
            Tasks.Clear();
            
            var input = new PagedResultRequestDto
            {
                SkipCount = 0,
                MaxResultCount = PageSize
            };

            var result = await TaskItemAppService.GetMyTasksDueTodayAsync(input);
            Tasks = result.Items.ToList();
            TotalTasksCount = (int)result.TotalCount;
            HasMoreData = Tasks.Count < TotalTasksCount;
            
            ApplyFilters();
        }
        catch (Exception ex)
        {
            await HandleErrorAsync(ex);
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task LoadMoreTasks()
    {
        if (IsLoadingMore || !HasMoreData) return;

        try
        {
            IsLoadingMore = true;
            CurrentPage++;

            var input = new PagedResultRequestDto
            {
                SkipCount = CurrentPage * PageSize,
                MaxResultCount = PageSize
            };

            var result = await TaskItemAppService.GetMyTasksDueTodayAsync(input);
            Tasks.AddRange(result.Items);
            HasMoreData = Tasks.Count < TotalTasksCount;
            
            ApplyFilters();
        }
        catch (Exception ex)
        {
            await HandleErrorAsync(ex);
        }
        finally
        {
            IsLoadingMore = false;
        }
    }

    private async Task RefreshTasks()
    {
        await LoadTasks();
        Snackbar.Add(L["TasksRefreshed"], Severity.Success);
    }

    private void ApplyFilters()
    {
        var filtered = Tasks.AsEnumerable();

        // Apply search filter
        if (!string.IsNullOrWhiteSpace(SearchText))
        {
            filtered = filtered.Where(t => 
                t.Title.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                t.Description.Contains(SearchText, StringComparison.OrdinalIgnoreCase));
        }

        // Apply status filter
        filtered = StatusFilter switch
        {
            TaskStatusFilter.Pending => filtered.Where(t => !t.IsCompleted),
            TaskStatusFilter.Completed => filtered.Where(t => t.IsCompleted),
            TaskStatusFilter.Overdue => filtered.Where(t => !t.IsCompleted && t.EndDate.HasValue && t.EndDate.Value.Date < DateTime.Today),
            _ => filtered
        };

        // Apply task type filter
        filtered = TaskTypeFilter switch
        {
            TaskTypeFilter.OneTime => filtered.Where(t => t.TaskType == TaskType.OneTime),
            TaskTypeFilter.Recurring => filtered.Where(t => t.TaskType == TaskType.Recurring),
            _ => filtered
        };

        FilteredTasks = filtered.ToList();
        StateHasChanged();
    }

    private async Task OnSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            ApplyFilters();
        }
    }

    private void OnSearchChanged()
    {
        ApplyFilters();
    }

    private void OnFilterChanged()
    {
        ApplyFilters();
    }

    private void ClearFilters()
    {
        SearchText = string.Empty;
        StatusFilter = TaskStatusFilter.All;
        TaskTypeFilter = TaskTypeFilter.All;
        ApplyFilters();
    }

    private async Task OnTaskDeleted()
    {
        await RefreshTasks();
    }

    private string GetEmptyStateIcon()
    {
        if (!Tasks.Any())
        {
            return Icons.Material.Filled.AssignmentTurnedIn;
        }

        return StatusFilter switch
        {
            TaskStatusFilter.Completed => Icons.Material.Filled.CheckCircle,
            TaskStatusFilter.Overdue => Icons.Material.Filled.Warning,
            _ => Icons.Material.Filled.Search
        };
    }

    private string GetEmptyStateTitle()
    {
        if (!Tasks.Any())
        {
            return L["NoTasksDueToday"];
        }

        return StatusFilter switch
        {
            TaskStatusFilter.Completed => L["NoCompletedTasks"],
            TaskStatusFilter.Pending => L["NoPendingTasks"],
            TaskStatusFilter.Overdue => L["NoOverdueTasks"],
            _ => L["NoTasksFound"]
        };
    }

    private string GetEmptyStateDescription()
    {
        if (!Tasks.Any())
        {
            return L["NoTasksDueTodayDescription"];
        }

        if (!string.IsNullOrWhiteSpace(SearchText))
        {
            return L["NoTasksMatchSearch"];
        }

        return StatusFilter switch
        {
            TaskStatusFilter.Completed => L["NoCompletedTasksDescription"],
            TaskStatusFilter.Pending => L["NoPendingTasksDescription"],
            TaskStatusFilter.Overdue => L["NoOverdueTasksDescription"],
            _ => L["NoTasksFoundDescription"]
        };
    }

    private new async Task HandleErrorAsync(Exception ex)
    {
        Console.WriteLine($"Error loading today's tasks: {ex.Message}");
        Snackbar.Add(L["ErrorLoadingTasks"], Severity.Error);
    }
}
