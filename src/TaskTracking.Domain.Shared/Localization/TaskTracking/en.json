{"culture": "en", "texts": {"AppName": "TaskTracking", "Menu:Home": "Home", "Welcome": "Welcome", "LongWelcomeMessage": "Welcome to the application. This is a startup project based on the ABP framework. For more information, visit abp.io.", "Permission:TaskTracking": "Task Tracking", "Permission:TaskGroups": "Task Groups", "Permission:TaskGroups.Create": "Create Task Groups", "Permission:TaskGroups.Update": "Update Task Groups", "Permission:TaskGroups.Delete": "Delete Task Groups", "Permission:TaskGroups.ManageUsers": "Manage Task Group Users", "Permission:TaskGroups.RecordProgress": "Record Task Progress", "Permission:TaskGroups.ManageTaskItems": "Manage Task Items", "Permission:TaskGroups.CreateTaskItems": "Create Task Items", "Permission:TaskGroups.UpdateTaskItems": "Update Task Items", "Permission:TaskGroups.DeleteTaskItems": "Delete Task Items", "Dashboard": "Dashboard", "TaskManagementSystem": "Task Management System", "WelcomeToDashboard": "Welcome to Task Dashboard", "ManageTaskGroupsEfficiently": "Manage task groups efficiently and elegantly", "ActiveTaskGroups": "Active Task Groups", "CreateNewGroup": "Create New Group", "LoadingTaskGroups": "Loading task groups...", "LoadMore": "Load More", "NoTaskGroups": "No Task Groups", "StartByCreatingTaskGroup": "Start by creating a new task group to organize your work", "CreateFirstTaskGroup": "Create First Task Group", "View": "View", "Edit": "Edit", "Options": "Options", "Completed": "Completed", "Expired": "Expired", "Active": "Active", "OverallProgress": "Overall Progress", "MainMenu": "Main Menu", "TaskGroups": "Task Groups", "AllGroups": "All Groups", "MyGroups": "My Groups", "CreateGroup": "Create Group", "Tasks": "Tasks", "TodayTasks": "Today's Tasks", "UpcomingTasks": "Upcoming Tasks", "CompletedTasks": "Completed Tasks", "Settings": "Settings", "Users": "Users", "Security": "Security", "About": "About System", "Profile": "Profile", "Logout": "Logout", "Notifications": "Notifications", "RTL": "RTL", "EditTaskGroup": "Edit Task Group", "UpdateTaskGroupDetails": "Update task group details and information", "LoadingTaskGroup": "Loading task group...", "TaskGroupNotFound": "Task group not found or you don't have permission to access it", "Title": "Title", "Description": "Description", "StartDate": "Start Date", "EndDate": "End Date", "EnterTaskGroupTitle": "Enter a descriptive title for the task group", "EnterTaskGroupDescription": "Enter a detailed description of the task group", "OptionalEndDate": "Optional end date for the task group", "Cancel": "Cancel", "SaveChanges": "Save Changes", "TaskGroupUpdatedSuccessfully": "Task group updated successfully!", "ErrorLoadingTaskGroup": "Error loading task group. Please try again.", "ErrorUpdatingTaskGroup": "Error updating task group. Please try again.", "EndDateMustBeAfterStartDate": "End date must be after start date", "CreateTaskGroup": "Create Task Group", "CreateNewTaskGroupDetails": "Create a new task group to organize your work", "SelectStartDate": "Select the start date for the task group", "CreateTaskGroupInfo": "You will be automatically assigned as the owner of this task group. You can add team members and assign tasks after creation.", "QuickStartTips": "Quick Start Tips", "TipChooseDescriptiveTitle": "Choose a clear and descriptive title that reflects the purpose of the task group", "TipAddDetailedDescription": "Add a detailed description to help team members understand the goals and scope", "TipSetRealisticDates": "Set realistic start and end dates based on your project timeline", "TipEndDateOptional": "End date is optional - leave it empty for ongoing or indefinite task groups", "TaskGroupCreatedSuccessfully": "Task group created successfully!", "ErrorCreatingTaskGroup": "Error creating task group. Please try again.", "StartDateCannotBeInPast": "Start date cannot be in the past", "ViewAllTaskGroupsInSystem": "Browse and explore all task groups in the system", "SearchTaskGroups": "Search task groups...", "Status": "Status", "AllStatuses": "All Statuses", "SortBy": "Sort By", "CreationDate": "Creation Date", "Progress": "Progress", "Filter": "Filter", "ShowingResults": "Showing {0} of {1} results", "ErrorLoadingTaskGroups": "Error loading task groups. Please try again.", "NoTaskGroupsFound": "No Task Groups Found", "NoTaskGroupsMatchingCriteria": "No task groups match your search criteria. Try adjusting your filters.", "ClearFilters": "Clear Filters", "ViewMyTaskGroups": "View and manage your own task groups", "TotalGroups": "Total Groups", "ActiveGroups": "Active Groups", "CompletedGroups": "Completed Groups", "AverageProgress": "Average Progress", "SearchMyGroups": "Search my groups...", "LoadingMyGroups": "Loading my groups...", "NoMyGroupsFound": "No Groups Found", "YouHaveNotCreatedAnyGroups": "You haven't created any task groups yet. Start by creating your first group!", "ErrorLoadingMyGroups": "Error loading your groups. Please try again.", "TaskGroupDetails": "Task Group Details", "ViewTaskGroupDetailsAndManageTasks": "View task group details and manage tasks", "TaskGroupNotFoundDescription": "The task group you're looking for doesn't exist or you don't have permission to access it.", "BackToMyGroups": "Back to My Groups", "InProgress": "In Progress", "NoDescriptionProvided": "No description provided", "LoadingTasks": "Loading tasks...", "NoTasksFound": "No Tasks Found", "NoTasksFoundDescription": "This task group doesn't have any tasks yet.", "CreateFirstTask": "Create First Task", "CreateTask": "Create Task", "AddTask": "Add Task", "AnErrorOccurred": "An error occurred", "OneTime": "One-time", "Recurring": "Recurring", "Overdue": "Overdue", "DueToday": "Due Today", "Pending": "Pending", "Recurrence": "Recurrence", "MarkComplete": "Mark Complete", "MarkIncomplete": "<PERSON>", "Daily": "Daily", "Weekly": "Weekly", "Monthly": "Monthly", "Yearly": "Yearly", "TrackingTask:00001": "Invalid date range. End date must be after start date.", "TrackingTask:00002": "Task end date exceeds group end date.", "TrackingTask:00003": "Cannot complete group with incomplete tasks.", "TrackingTask:00101": "<PERSON><PERSON> set recurrence pattern for one-time task.", "TrackingTask:00102": "Recurrence pattern is required for recurring tasks.", "TrackingTask:00103": "Progress already recorded for this date.", "TrackingTask:00201": "Invalid recurrence interval. Must be greater than 0.", "TrackingTask:00202": "Invalid recurrence occurrences. Must be greater than 0.", "TrackingTask:00203": "Weekly recurrence requires days of week to be specified.", "TrackingTask:00204": "Recurrence end date exceeds task item end date.", "TrackingTask:00301": "Cannot remove owner from group.", "TrackingTask:00302": "User is already in the group.", "TrackingTask:00401": "Invalid progress percentage. Must be between 0 and 100.", "TrackingTask:00402": "User is not in the group.", "TrackingTask:00403": "Task is not in the group.", "TrackingTask:00404": "Progress already exists for this user and task.", "TrackingTask:00405": "Progress not found.", "TrackingTask:00406": "Cannot change owner role.", "Custom": "Custom", "TaskType": "Task Type", "RecurrenceType": "Recurrence Type", "Interval": "Interval", "DaysOfWeek": "Days of Week", "Occurrences": "Occurrences", "Date": "Date", "TaskItemId": "Task Item ID", "The {PropertyName} field is required.": "The {PropertyName} field is required.", "The {PropertyName} field must not exceed {MaxLength} characters.": "The {PropertyName} field must not exceed {MaxLength} characters.", "CreateNewTaskInGroup": "Create a new task in this group", "ViewGroup": "View Group", "CreatingTaskForGroup": "Creating a new task for this group", "EnterTaskTitle": "Enter a descriptive title for the task", "EnterTaskDescription": "Enter a detailed description of the task", "OneTimeTask": "One-time Task", "OneTimeTaskDescription": "A task that needs to be completed once", "RecurringTask": "Recurring Task", "RecurringTaskDescription": "A task that repeats on a schedule", "RecurrencePattern": "Recurrence <PERSON>", "SelectRecurrenceType": "Select how often this task repeats", "EveryNDays": "Every N days (e.g., every 2 days)", "EveryNWeeks": "Every N weeks (e.g., every 3 weeks)", "EveryNMonths": "Every N months (e.g., every 2 months)", "EndCondition": "End Condition", "EndByDate": "End by specific date", "EndAfterOccurrences": "End after number of occurrences", "RecurrenceEndDate": "Recurrence End Date", "SelectRecurrenceEndDate": "Select when the recurrence should end", "NumberOfOccurrences": "Number of Occurrences", "EnterNumberOfOccurrences": "Enter how many times this task should repeat", "CreateTaskInfo": "This task will be added to the current task group. All group members will be able to see and work on this task.", "TaskCreationTips": "Task Creation Tips", "TipChooseDescriptiveTaskTitle": "Choose a clear and specific title that describes what needs to be done", "TipAddDetailedTaskDescription": "Add detailed instructions and requirements to help team members understand the task", "TipSetRealisticTaskDates": "Set realistic start and end dates based on task complexity and dependencies", "TipRecurringTasksHelp": "Use recurring tasks for regular activities like reviews, reports, or maintenance", "TaskCreatedSuccessfully": "Task created successfully!", "ErrorCreatingTask": "Error creating task. Please try again.", "EditTask": "Edit Task", "UpdateTaskDetails": "Update task details and settings", "LoadingTask": "Loading task...", "TaskNotFound": "Task Not Found", "TaskNotFoundDescription": "The requested task could not be found or you don't have permission to access it.", "BackToGroup": "Back to Group", "EditingTaskInGroup": "Editing task in this group", "EditTaskInfo": "Changes to this task will be visible to all group members. Be careful when modifying recurring tasks as it may affect future occurrences.", "TaskEditingTips": "Task Editing Tips", "TipChangingTaskType": "Changing task type from recurring to one-time will remove the recurrence pattern", "TipRecurrencePatternChanges": "Modifying recurrence patterns affects future task instances, not completed ones", "TipDateChangesAffectProgress": "Changing dates may affect progress tracking and due date calculations", "TipSaveChangesRegularly": "Save changes regularly to avoid losing your work", "TaskUpdatedSuccessfully": "Task updated successfully!", "ErrorUpdatingTask": "Error updating task. Please try again.", "TaskDetails": "Task Details", "ViewTaskDetailsAndInformation": "View task details and information", "NoEndDate": "No end date", "NoEndCondition": "No end condition", "NoProgressRecorded": "No progress recorded yet", "ProgressDescription": "{0} of {1} days completed", "ViewTask": "View Task", "ErrorLoadingTask": "Error loading task. Please try again.", "Sunday": "Sunday", "Monday": "Monday", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "Thursday": "Thursday", "Friday": "Friday", "Saturday": "Saturday", "RecurrenceMustHaveEndDateOrOccurrences": "Recurrence pattern must have either an end date or number of occurrences", "The {PropertyName} field has an invalid value.": "The {PropertyName} field has an invalid value.", "Days of week should only be specified for weekly recurrence.": "Days of week should only be specified for weekly recurrence.", "Recurrence must have an end date or a number of occurrences.": "Recurrence must have an end date or a number of occurrences.", "Recurrence cannot have both end date and number of occurrences.": "Recurrence cannot have both end date and number of occurrences.", "Progress date cannot be in the future.": "Progress date cannot be in the future.", "TrackingTask:00205": "Recurrence must have an end date or a number of occurrences.", "TaskRecurrence_EveryNDays": "Every {0} days", "TaskRecurrence_EveryNWeeks": "Every {0} weeks", "TaskRecurrence_EveryNMonths": "Every {0} months", "TaskRecurrence_NoRecurrence": "No recurrence", "RecordProgress": "Record Progress", "CurrentProgress": "Current Progress", "QuickActions": "Quick Actions", "MarkCompletedToday": "Mark Completed Today", "ProgressCalendar": "Progress Calendar", "SelectCompletionDate": "Select Completion Date", "CompletionDate": "Completion Date", "TaskFullyCompleted": "This task has been fully completed!", "ProgressRecordedSuccessfully": "Progress recorded successfully!", "ErrorRecordingProgress": "Error recording progress. Please try again.", "TaskCompleted": "Task completed", "ProgressRemaining": "{0} remaining", "Due": "Due", "ErrorLoadingProgressDetail": "Error loading progress details. Please try again.", "Close": "Close", "TaskTracking:00104": "Progress date cannot be in the future.", "Delete": "Delete", "DeleteTask": "Delete Task", "DeleteTaskConfirmation": "Are you sure you want to delete the task '{0}'? This action cannot be undone.", "TaskDeletedSuccessfully": "Task deleted successfully!", "ProgressRemovedSuccessfully": "Progress removed successfully!", "ErrorRemovingProgress": "Error removing progress. Please try again.", "ViewTasksDueToday": "View all tasks that are due today", "TasksDueToday": "Tasks Due Today", "SearchTasks": "Search tasks...", "AllTasks": "All Tasks", "AllTypes": "All Types", "Refresh": "Refresh", "TasksRefreshed": "Tasks refreshed successfully!", "LoadingMoreTasks": "Loading more tasks...", "NoTasksDueToday": "No Tasks Due Today", "NoTasksDueTodayDescription": "You don't have any tasks due today. Great job staying on top of your work!", "NoCompletedTasks": "No Completed Tasks", "NoCompletedTasksDescription": "No completed tasks found with the current filters.", "NoPendingTasks": "No Pending Tasks", "NoPendingTasksDescription": "No pending tasks found with the current filters.", "NoOverdueTasks": "No Overdue Tasks", "NoOverdueTasksDescription": "No overdue tasks found with the current filters.", "NoTasksMatchSearch": "No tasks match your search criteria. Try adjusting your search terms or filters.", "ErrorLoadingTasks": "Error loading tasks. Please try again."}}